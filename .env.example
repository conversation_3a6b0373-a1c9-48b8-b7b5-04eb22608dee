# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/id_provider?schema=public"

# Redis 缓存配置
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""
REDIS_DB=0
REDIS_KEY_PREFIX="idp:"

# JWT 配置
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-this-in-production"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# 服务器配置
PORT=3000
NODE_ENV="development"

# 邮件配置 (SMTP)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"

# 短信配置 (Twilio)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# Google OAuth 配置
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_CALLBACK_URL="http://localhost:3000/api/v1/auth/google/callback"

# GitHub OAuth 配置
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
GITHUB_CALLBACK_URL="http://localhost:3000/api/v1/auth/github/callback"

# 微信开放平台配置
WECHAT_APP_ID="your-wechat-app-id"
WECHAT_APP_SECRET="your-wechat-app-secret"
WECHAT_CALLBACK_URL="http://localhost:3000/api/v1/auth/wechat/callback"

# 微博开放平台配置
WEIBO_CLIENT_ID="your-weibo-client-id"
WEIBO_CLIENT_SECRET="your-weibo-client-secret"
WEIBO_CALLBACK_URL="http://localhost:3000/api/v1/auth/weibo/callback"

# 前端URL配置
FRONTEND_URL="http://localhost:3001"

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 零信任模式配置
ZERO_TRUST_ENABLED=false
RISK_THRESHOLD_LOW=30
RISK_THRESHOLD_MEDIUM=60
RISK_THRESHOLD_HIGH=80

# 会话配置
SESSION_TIMEOUT_MINUTES=30
REMEMBER_ME_DAYS=30

# SAML 配置
SAML_CERT_PATH="./certs/saml.crt"
SAML_KEY_PATH="./certs/saml.key"
SAML_ISSUER="http://localhost:3000"

# 日志配置
LOG_LEVEL="info"
LOG_FILE_PATH="./logs/app.log"

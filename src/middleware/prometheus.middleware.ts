/**
 * Prometheus监控中间件
 * 自动收集HTTP请求和响应指标
 */

import { Request, Response, NextFunction } from 'express';
import { prometheusService } from '@/services/prometheus.service';
import { logger } from '@/config/logger';

interface PrometheusRequest extends Request {
  startTime?: number;
  route?: {
    path?: string;
  };
}

/**
 * Prometheus指标收集中间件
 */
export const prometheusMiddleware = (req: PrometheusRequest, res: Response, next: NextFunction): void => {
  // 记录请求开始时间
  req.startTime = Date.now();

  // 增加正在处理的请求计数
  prometheusService.incrementHttpRequestsInFlight();

  // 监听响应完成事件
  res.on('finish', () => {
    try {
      // 减少正在处理的请求计数
      prometheusService.decrementHttpRequestsInFlight();

      // 计算请求持续时间
      const duration = Date.now() - (req.startTime || Date.now());

      // 获取路由路径
      const route = getRoutePath(req);

      // 记录HTTP请求指标
      prometheusService.recordHttpRequest(
        req.method,
        route,
        res.statusCode,
        duration
      );

      // 记录详细日志（仅在调试模式下）
      if (process.env.NODE_ENV === 'development') {
        logger.debug('Prometheus指标已记录', {
          service: 'prometheus',
          method: req.method,
          route,
          statusCode: res.statusCode,
          duration: `${duration}ms`
        });
      }

    } catch (error) {
      logger.error('Prometheus中间件处理失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error),
        method: req.method,
        url: req.url
      });
    }
  });

  // 监听响应错误事件
  res.on('error', (error) => {
    try {
      // 减少正在处理的请求计数
      prometheusService.decrementHttpRequestsInFlight();

      logger.error('HTTP响应错误', {
        service: 'prometheus',
        error: error.message,
        method: req.method,
        url: req.url
      });

    } catch (err) {
      logger.error('处理响应错误失败', {
        service: 'prometheus',
        error: err instanceof Error ? err.message : String(err)
      });
    }
  });

  next();
};

/**
 * 获取标准化的路由路径
 */
function getRoutePath(req: PrometheusRequest): string {
  try {
    // 尝试从路由对象获取路径
    if (req.route?.path) {
      return req.route.path;
    }

    // 从URL中提取路径并标准化
    const url = req.url || req.originalUrl || '/';
    const path = url.split('?')[0]; // 移除查询参数

    // 标准化常见的API路径
    return standardizeApiPath(path);

  } catch (error) {
    logger.error('获取路由路径失败', {
      service: 'prometheus',
      error: error instanceof Error ? error.message : String(error),
      url: req.url
    });
    return '/unknown';
  }
}

/**
 * 标准化API路径，将动态参数替换为占位符
 */
function standardizeApiPath(path: string): string {
  try {
    // 移除尾部斜杠
    path = path.replace(/\/$/, '') || '/';

    // 标准化常见的API路径模式
    const patterns = [
      // API版本路径
      { pattern: /^\/api\/v\d+/, replacement: '/api/v*' },
      
      // 用户ID路径
      { pattern: /\/users\/[a-f0-9-]{36}/, replacement: '/users/:id' },
      { pattern: /\/users\/\d+/, replacement: '/users/:id' },
      
      // OAuth客户端ID路径
      { pattern: /\/oauth-clients\/[a-f0-9-]{36}/, replacement: '/oauth-clients/:id' },
      { pattern: /\/oauth-clients\/[\w-]+/, replacement: '/oauth-clients/:id' },
      
      // 会话ID路径
      { pattern: /\/sessions\/[a-f0-9-]{36}/, replacement: '/sessions/:id' },
      
      // 审计日志ID路径
      { pattern: /\/audit-logs\/[a-f0-9-]{36}/, replacement: '/audit-logs/:id' },
      
      // 通用UUID路径
      { pattern: /\/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/, replacement: '/:id' },
      
      // 数字ID路径
      { pattern: /\/\d+/, replacement: '/:id' },
      
      // 文件扩展名
      { pattern: /\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/, replacement: '/*' }
    ];

    // 应用标准化模式
    for (const { pattern, replacement } of patterns) {
      if (pattern.test(path)) {
        path = path.replace(pattern, replacement);
        break; // 只应用第一个匹配的模式
      }
    }

    // 限制路径长度
    if (path.length > 100) {
      path = path.substring(0, 97) + '...';
    }

    return path;

  } catch (error) {
    logger.error('标准化API路径失败', {
      service: 'prometheus',
      error: error instanceof Error ? error.message : String(error),
      originalPath: path
    });
    return '/error';
  }
}

/**
 * 认证指标中间件
 * 记录认证相关的指标
 */
export const authMetricsMiddleware = (method: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 监听响应完成事件
    res.on('finish', () => {
      try {
        const success = res.statusCode >= 200 && res.statusCode < 300;
        prometheusService.recordAuthAttempt(method, success);

      } catch (error) {
        logger.error('记录认证指标失败', {
          service: 'prometheus',
          error: error instanceof Error ? error.message : String(error),
          method,
          statusCode: res.statusCode
        });
      }
    });

    next();
  };
};

/**
 * 数据库指标装饰器
 * 用于装饰数据库操作函数以收集指标
 */
export function withDatabaseMetrics<T extends any[], R>(
  operation: string,
  table: string,
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    let success = false;

    try {
      const result = await fn(...args);
      success = true;
      return result;

    } catch (error) {
      success = false;
      throw error;

    } finally {
      try {
        const duration = Date.now() - startTime;
        prometheusService.recordDatabaseQuery(operation, table, duration, success);

      } catch (metricsError) {
        logger.error('记录数据库指标失败', {
          service: 'prometheus',
          error: metricsError instanceof Error ? metricsError.message : String(metricsError),
          operation,
          table
        });
      }
    }
  };
}

/**
 * Redis指标装饰器
 * 用于装饰Redis操作函数以收集指标
 */
export function withRedisMetrics<T extends any[], R>(
  command: string,
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    let success = false;

    try {
      const result = await fn(...args);
      success = true;
      return result;

    } catch (error) {
      success = false;
      throw error;

    } finally {
      try {
        const duration = Date.now() - startTime;
        prometheusService.recordRedisCommand(command, duration, success);

      } catch (metricsError) {
        logger.error('记录Redis指标失败', {
          service: 'prometheus',
          error: metricsError instanceof Error ? metricsError.message : String(metricsError),
          command
        });
      }
    }
  };
}

export default prometheusMiddleware;

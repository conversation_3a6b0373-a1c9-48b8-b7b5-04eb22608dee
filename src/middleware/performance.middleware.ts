/**
 * 性能监控中间件
 * 监控API响应时间、数据库查询性能、内存使用等指标
 */

import { Request, Response, NextFunction } from 'express';
import { logger, logPerformance } from '@/config/logger';
import { cacheService } from '@/services/cache.service';
import { prisma } from '@/config/database';

// 性能指标收集器
class PerformanceCollector {
  private metrics: Map<string, any[]> = new Map();
  private readonly maxMetrics = 1000; // 最多保存1000个指标

  /**
   * 添加性能指标
   */
  addMetric(type: string, data: any): void {
    if (!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }

    const typeMetrics = this.metrics.get(type)!;
    typeMetrics.push({
      ...data,
      timestamp: Date.now()
    });

    // 保持指标数量在限制内
    if (typeMetrics.length > this.maxMetrics) {
      typeMetrics.shift();
    }
  }

  /**
   * 获取性能统计
   */
  getStats(type: string, timeWindow: number = 5 * 60 * 1000): any {
    const metrics = this.metrics.get(type) || [];
    const now = Date.now();
    const recentMetrics = metrics.filter(m => now - m.timestamp <= timeWindow);

    if (recentMetrics.length === 0) {
      return null;
    }

    const durations = recentMetrics.map(m => m.duration).filter(d => d !== undefined);
    
    if (durations.length === 0) {
      return { count: recentMetrics.length };
    }

    durations.sort((a, b) => a - b);

    return {
      count: recentMetrics.length,
      avg: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      min: durations[0],
      max: durations[durations.length - 1],
      p50: durations[Math.floor(durations.length * 0.5)],
      p95: durations[Math.floor(durations.length * 0.95)],
      p99: durations[Math.floor(durations.length * 0.99)]
    };
  }

  /**
   * 获取所有性能统计
   */
  getAllStats(timeWindow: number = 5 * 60 * 1000): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [type] of this.metrics) {
      stats[type] = this.getStats(type, timeWindow);
    }

    return stats;
  }

  /**
   * 清理过期指标
   */
  cleanup(maxAge: number = 60 * 60 * 1000): void {
    const now = Date.now();
    
    for (const [type, metrics] of this.metrics) {
      const validMetrics = metrics.filter(m => now - m.timestamp <= maxAge);
      this.metrics.set(type, validMetrics);
    }
  }
}

// 全局性能收集器实例
export const performanceCollector = new PerformanceCollector();

/**
 * API响应时间监控中间件
 */
export const responseTimeMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();

  // 监听响应完成事件
  res.on('finish', () => {
    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    const duration = endTime - startTime;

    // 收集基础指标
    const metrics = {
      method: req.method,
      path: req.route?.path || req.path,
      statusCode: res.statusCode,
      duration,
      memoryDelta: {
        rss: endMemory.rss - startMemory.rss,
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal
      },
      userAgent: req.get('User-Agent'),
      ip: req.ip
    };

    // 添加到性能收集器
    performanceCollector.addMetric('api_response', metrics);

    // 记录慢请求
    if (duration > 1000) {
      logger.warn('慢请求检测', {
        ...metrics,
        threshold: 1000
      });
    }

    // 记录性能日志
    logPerformance(`${req.method} ${req.path}`, duration, {
      statusCode: res.statusCode,
      memoryDelta: metrics.memoryDelta
    });
  });

  next();
};

/**
 * 数据库查询性能监控
 */
export class DatabasePerformanceMonitor {
  private static instance: DatabasePerformanceMonitor;
  private queryCount = 0;
  private slowQueryThreshold = 100; // 100ms

  static getInstance(): DatabasePerformanceMonitor {
    if (!DatabasePerformanceMonitor.instance) {
      DatabasePerformanceMonitor.instance = new DatabasePerformanceMonitor();
    }
    return DatabasePerformanceMonitor.instance;
  }

  /**
   * 初始化数据库性能监控
   */
  init(): void {
    // 监听Prisma查询事件
    prisma.$on('query', (e) => {
      this.queryCount++;
      
      const metrics = {
        query: e.query,
        duration: e.duration,
        target: e.target,
        queryCount: this.queryCount
      };

      // 添加到性能收集器
      performanceCollector.addMetric('db_query', metrics);

      // 记录慢查询
      if (e.duration > this.slowQueryThreshold) {
        logger.warn('慢查询检测', {
          ...metrics,
          threshold: this.slowQueryThreshold
        });
      }
    });

    logger.info('数据库性能监控已初始化');
  }

  /**
   * 获取数据库性能统计
   */
  getStats(): any {
    return {
      totalQueries: this.queryCount,
      recentStats: performanceCollector.getStats('db_query'),
      slowQueryThreshold: this.slowQueryThreshold
    };
  }

  /**
   * 设置慢查询阈值
   */
  setSlowQueryThreshold(threshold: number): void {
    this.slowQueryThreshold = threshold;
    logger.info('慢查询阈值已更新', { threshold });
  }
}

/**
 * 内存使用监控
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private intervalId?: NodeJS.Timeout;

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  /**
   * 开始内存监控
   */
  start(interval: number = 30000): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(() => {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      const metrics = {
        memory: {
          rss: memoryUsage.rss,
          heapUsed: memoryUsage.heapUsed,
          heapTotal: memoryUsage.heapTotal,
          external: memoryUsage.external,
          arrayBuffers: memoryUsage.arrayBuffers
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        }
      };

      // 添加到性能收集器
      performanceCollector.addMetric('system_resources', metrics);

      // 检查内存使用是否过高
      const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
      if (heapUsedMB > 500) { // 500MB阈值
        logger.warn('高内存使用检测', {
          heapUsedMB,
          threshold: 500
        });
      }

    }, interval);

    logger.info('内存监控已启动', { interval });
  }

  /**
   * 停止内存监控
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
      logger.info('内存监控已停止');
    }
  }

  /**
   * 获取当前内存使用情况
   */
  getCurrentUsage(): any {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      memory: {
        rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,
        heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
        external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`
      },
      cpu: {
        user: `${(cpuUsage.user / 1000).toFixed(2)} ms`,
        system: `${(cpuUsage.system / 1000).toFixed(2)} ms`
      },
      uptime: `${(process.uptime() / 60).toFixed(2)} minutes`
    };
  }
}

/**
 * 缓存性能监控
 */
export class CachePerformanceMonitor {
  private static instance: CachePerformanceMonitor;
  private hitCount = 0;
  private missCount = 0;

  static getInstance(): CachePerformanceMonitor {
    if (!CachePerformanceMonitor.instance) {
      CachePerformanceMonitor.instance = new CachePerformanceMonitor();
    }
    return CachePerformanceMonitor.instance;
  }

  /**
   * 记录缓存命中
   */
  recordHit(key: string, duration: number): void {
    this.hitCount++;
    
    performanceCollector.addMetric('cache_hit', {
      key,
      duration,
      type: 'hit'
    });
  }

  /**
   * 记录缓存未命中
   */
  recordMiss(key: string, duration: number): void {
    this.missCount++;
    
    performanceCollector.addMetric('cache_miss', {
      key,
      duration,
      type: 'miss'
    });
  }

  /**
   * 获取缓存性能统计
   */
  getStats(): any {
    const total = this.hitCount + this.missCount;
    const hitRate = total > 0 ? (this.hitCount / total) * 100 : 0;

    return {
      hitCount: this.hitCount,
      missCount: this.missCount,
      total,
      hitRate: `${hitRate.toFixed(2)}%`,
      hitStats: performanceCollector.getStats('cache_hit'),
      missStats: performanceCollector.getStats('cache_miss')
    };
  }

  /**
   * 重置统计
   */
  reset(): void {
    this.hitCount = 0;
    this.missCount = 0;
    logger.info('缓存性能统计已重置');
  }
}

/**
 * 性能监控初始化
 */
export function initializePerformanceMonitoring(): void {
  // 初始化数据库性能监控
  DatabasePerformanceMonitor.getInstance().init();

  // 启动内存监控
  MemoryMonitor.getInstance().start();

  // 定期清理过期指标
  setInterval(() => {
    performanceCollector.cleanup();
  }, 60 * 60 * 1000); // 每小时清理一次

  logger.info('性能监控系统已初始化');
}

/**
 * 获取综合性能报告
 */
export function getPerformanceReport(): any {
  return {
    timestamp: new Date().toISOString(),
    api: performanceCollector.getStats('api_response'),
    database: DatabasePerformanceMonitor.getInstance().getStats(),
    cache: CachePerformanceMonitor.getInstance().getStats(),
    system: MemoryMonitor.getInstance().getCurrentUsage(),
    overview: performanceCollector.getAllStats()
  };
}

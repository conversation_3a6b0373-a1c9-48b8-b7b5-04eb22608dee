/**
 * 安全中间件
 * 实施各种安全策略和保护措施
 */

import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { securityConfig } from '../config/security';
import { logger } from '../config/logger';
import { config } from '../config';

/**
 * 登录速率限制中间件
 */
export const loginRateLimit = rateLimit({
  windowMs: securityConfig.rateLimit.login.windowMs,
  max: securityConfig.rateLimit.login.max,
  message: {
    error: 'too_many_attempts',
    message: '登录尝试次数过多，请稍后再试',
    retryAfter: Math.ceil(securityConfig.rateLimit.login.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: securityConfig.rateLimit.login.skipSuccessfulRequests,
  keyGenerator: (req: Request) => {
    // 使用IP和用户名组合作为限制键
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const username = req.body?.username || req.body?.email || 'anonymous';
    return `login:${ip}:${username}`;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('登录速率限制触发', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      username: req.body?.username || req.body?.email
    });

    res.status(429).json({
      error: 'too_many_attempts',
      message: '登录尝试次数过多，请稍后再试',
      retryAfter: Math.ceil(securityConfig.rateLimit.login.windowMs / 1000)
    });
  }
});

/**
 * 注册速率限制中间件
 */
export const registerRateLimit = rateLimit({
  windowMs: securityConfig.rateLimit.register.windowMs,
  max: securityConfig.rateLimit.register.max,
  message: {
    error: 'too_many_registrations',
    message: '注册请求过于频繁，请稍后再试',
    retryAfter: Math.ceil(securityConfig.rateLimit.register.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => req.ip || 'unknown',
  handler: (req: Request, res: Response) => {
    logger.warn('注册速率限制触发', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      email: req.body?.email
    });

    res.status(429).json({
      error: 'too_many_registrations',
      message: '注册请求过于频繁，请稍后再试',
      retryAfter: Math.ceil(securityConfig.rateLimit.register.windowMs / 1000)
    });
  }
});

/**
 * API通用速率限制中间件
 */
export const apiRateLimit = rateLimit({
  windowMs: securityConfig.rateLimit.api.windowMs,
  max: securityConfig.rateLimit.api.max,
  message: {
    error: 'too_many_requests',
    message: 'API请求过于频繁，请稍后再试',
    retryAfter: Math.ceil(securityConfig.rateLimit.api.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => req.ip || 'unknown'
});

/**
 * 密码重置速率限制中间件
 */
export const passwordResetRateLimit = rateLimit({
  windowMs: securityConfig.rateLimit.passwordReset.windowMs,
  max: securityConfig.rateLimit.passwordReset.max,
  message: {
    error: 'too_many_password_resets',
    message: '密码重置请求过于频繁，请稍后再试',
    retryAfter: Math.ceil(securityConfig.rateLimit.passwordReset.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    const ip = req.ip || 'unknown';
    const email = req.body?.email || 'anonymous';
    return `password-reset:${ip}:${email}`;
  }
});

/**
 * 通用API速率限制中间件 (用于一般API端点)
 */
export const generalApiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    error: 'rate_limit_exceeded',
    message: '请求过于频繁，请稍后再试',
    retryAfter: Math.ceil(15 * 60)
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => req.ip || 'unknown'
});

/**
 * 请求减速中间件 (在达到限制前先减速)
 */
export const requestSlowDown = slowDown({
  windowMs: 15 * 60 * 1000, // 15分钟
  delayAfter: 50, // 50次请求后开始减速
  delayMs: () => 500, // 每次增加500ms延迟
  maxDelayMs: 5000, // 最大延迟5秒
  skipFailedRequests: false,
  skipSuccessfulRequests: false,
  validate: { delayMs: false } // 禁用delayMs警告
});

/**
 * 安全头中间件
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // 设置安全头
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', securityConfig.headers.frameOptions);
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', securityConfig.headers.referrerPolicy);
  
  // 生产环境启用HSTS
  if (config.server.nodeEnv === 'production') {
    res.setHeader('Strict-Transport-Security', 
      `max-age=${securityConfig.headers.hsts.maxAge}; includeSubDomains; preload`);
  }

  // 权限策略
  const permissionsPolicy = Object.entries(securityConfig.headers.permissionsPolicy)
    .map(([directive, allowlist]) => `${directive}=(${allowlist.join(' ')})`)
    .join(', ');
  res.setHeader('Permissions-Policy', permissionsPolicy);

  next();
};

/**
 * IP过滤中间件
 */
export const ipFilter = (req: Request, res: Response, next: NextFunction) => {
  if (!securityConfig.ipFilter.enabled) {
    return next();
  }

  const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
  
  // 检查黑名单
  if (securityConfig.ipFilter.blacklist.includes(clientIp)) {
    logger.warn('IP黑名单访问被阻止', { ip: clientIp, path: req.path });
    return res.status(403).json({
      error: 'access_denied',
      message: '访问被拒绝'
    });
  }

  // 检查白名单 (如果配置了白名单)
  if (securityConfig.ipFilter.whitelist.length > 0 && 
      !securityConfig.ipFilter.whitelist.includes(clientIp)) {
    logger.warn('IP不在白名单中', { ip: clientIp, path: req.path });
    return res.status(403).json({
      error: 'access_denied',
      message: '访问被拒绝'
    });
  }

  next();
};

/**
 * 请求大小限制中间件
 */
export const requestSizeLimit = (req: Request, res: Response, next: NextFunction) => {
  const contentLength = parseInt(req.get('content-length') || '0', 10);
  const maxSize = 10 * 1024 * 1024; // 10MB

  if (contentLength > maxSize) {
    logger.warn('请求体过大', { 
      ip: req.ip, 
      contentLength, 
      maxSize,
      path: req.path 
    });
    return res.status(413).json({
      error: 'payload_too_large',
      message: '请求体过大',
      maxSize: maxSize
    });
  }

  next();
};

/**
 * 可疑活动检测中间件
 */
export const suspiciousActivityDetection = (req: Request, res: Response, next: NextFunction) => {
  const userAgent = req.get('User-Agent') || '';
  const ip = req.ip || 'unknown';
  
  // 检测可疑的User-Agent
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
    /go-http-client/i
  ];

  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));
  
  if (isSuspicious && !req.path.startsWith('/api/docs')) {
    logger.warn('检测到可疑活动', {
      ip,
      userAgent,
      path: req.path,
      method: req.method
    });
    
    // 不直接阻止，但记录并可能触发额外验证
    req.headers['x-suspicious-activity'] = 'true';
  }

  next();
};

/**
 * CSRF保护中间件 (简化版)
 */
export const csrfProtection = (req: Request, res: Response, next: NextFunction) => {
  // 跳过GET、HEAD、OPTIONS请求
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // 跳过API文档和健康检查
  if (req.path.startsWith('/api/docs') || req.path === '/health') {
    return next();
  }

  const token = req.get('X-CSRF-Token') || req.body._csrf;
  const sessionToken = req.session?.csrfToken;

  if (!token || !sessionToken || token !== sessionToken) {
    logger.warn('CSRF令牌验证失败', {
      ip: req.ip,
      path: req.path,
      hasToken: !!token,
      hasSessionToken: !!sessionToken
    });
    
    return res.status(403).json({
      error: 'csrf_token_invalid',
      message: 'CSRF令牌无效'
    });
  }

  next();
};

/**
 * 安全审计中间件
 */
export const securityAudit = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // 记录请求信息
  const requestInfo = {
    ip: req.ip,
    method: req.method,
    path: req.path,
    userAgent: req.get('User-Agent'),
    referer: req.get('Referer'),
    timestamp: new Date().toISOString(),
    suspicious: req.headers['x-suspicious-activity'] === 'true'
  };

  // 监听响应完成
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const auditLog = {
      ...requestInfo,
      statusCode: res.statusCode,
      duration,
      contentLength: res.get('content-length')
    };

    // 记录失败的请求
    if (res.statusCode >= 400) {
      logger.warn('HTTP请求失败', auditLog);
    } else if (securityConfig.audit.logSuccessfulLogins && req.path.includes('/login')) {
      logger.info('登录请求', auditLog);
    }
  });

  next();
};

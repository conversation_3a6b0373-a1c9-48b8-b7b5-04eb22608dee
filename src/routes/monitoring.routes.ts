/**
 * 监控路由
 * 提供系统性能监控和健康检查的API端点
 */

import { Router } from 'express';
import { MonitoringController } from '@/controllers/monitoring.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();
const monitoringController = new MonitoringController();

// 监控端点速率限制（相对宽松，因为监控需要频繁访问）
const monitoringRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 100, // 每分钟最多100次请求
  message: {
    error: 'monitoring_rate_limit_exceeded',
    message: '监控请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 健康检查速率限制（更宽松）
const healthCheckRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 200, // 每分钟最多200次请求
  message: {
    error: 'health_check_rate_limit_exceeded',
    message: '健康检查请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route GET /api/v1/monitoring/health
 * @desc 系统健康检查
 * @access Public
 * @returns {Object} 系统健康状态信息
 */
router.get('/health', 
  healthCheckRateLimit,
  monitoringController.health
);

/**
 * @route GET /api/v1/monitoring/metrics
 * @desc 获取系统性能指标
 * @access Private (需要认证)
 * @query {number} [timeWindow] - 时间窗口（秒），默认300秒
 * @returns {Object} 系统性能指标报告
 */
router.get('/metrics',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.metrics
);

/**
 * @route GET /api/v1/monitoring/database
 * @desc 获取数据库性能指标
 * @access Private (需要认证)
 * @returns {Object} 数据库性能指标
 */
router.get('/database',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.databaseMetrics
);

/**
 * @route GET /api/v1/monitoring/cache
 * @desc 获取缓存性能指标
 * @access Private (需要认证)
 * @returns {Object} 缓存性能指标
 */
router.get('/cache',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.cacheMetrics
);

/**
 * @route GET /api/v1/monitoring/system
 * @desc 获取系统资源使用情况
 * @access Private (需要认证)
 * @returns {Object} 系统资源指标
 */
router.get('/system',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.systemMetrics
);

/**
 * @route GET /api/v1/monitoring/realtime
 * @desc 实时性能监控 (Server-Sent Events)
 * @access Private (需要认证)
 * @returns {Stream} 实时性能数据流
 */
router.get('/realtime',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.realtime
);

/**
 * @route GET /api/v1/monitoring/alerts
 * @desc 获取报警配置
 * @access Private (需要认证)
 * @returns {Object} 报警配置信息
 */
router.get('/alerts',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.alerts
);

/**
 * @route POST /api/v1/monitoring/alerts
 * @desc 更新报警配置
 * @access Private (需要认证)
 * @body {Object} 报警配置
 * @returns {Object} 更新结果
 */
router.post('/alerts',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.alerts
);

export default router;

/**
 * 数据验证工具
 * 使用Joi进行请求数据验证
 */

import <PERSON><PERSON> from 'joi';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  data?: any;
}

/**
 * 用户注册验证模式
 */
const registerSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '邮箱格式不正确',
      'any.required': '邮箱是必需的'
    }),
  
  password: Joi.string()
    .min(8)
    .max(128)
    .required()
    .messages({
      'string.min': '密码长度至少8个字符',
      'string.max': '密码长度不能超过128个字符',
      'any.required': '密码是必需的'
    }),
  
  nickname: Joi.string()
    .min(1)
    .max(50)
    .optional()
    .messages({
      'string.min': '昵称不能为空',
      'string.max': '昵称长度不能超过50个字符'
    }),
  
  firstName: Joi.string()
    .min(1)
    .max(50)
    .optional()
    .messages({
      'string.min': '名字不能为空',
      'string.max': '名字长度不能超过50个字符'
    }),
  
  lastName: Joi.string()
    .min(1)
    .max(50)
    .optional()
    .messages({
      'string.min': '姓氏不能为空',
      'string.max': '姓氏长度不能超过50个字符'
    })
});

/**
 * 用户登录验证模式
 */
const loginSchema = Joi.object({
  username: Joi.string()
    .required()
    .messages({
      'any.required': '用户名/邮箱是必需的'
    }),
  
  password: Joi.string()
    .required()
    .messages({
      'any.required': '密码是必需的'
    }),
  
  rememberMe: Joi.boolean()
    .optional()
    .default(false)
});

/**
 * 密码修改验证模式
 */
const changePasswordSchema = Joi.object({
  currentPassword: Joi.string()
    .required()
    .messages({
      'any.required': '当前密码是必需的'
    }),
  
  newPassword: Joi.string()
    .min(8)
    .max(128)
    .required()
    .messages({
      'string.min': '新密码长度至少8个字符',
      'string.max': '新密码长度不能超过128个字符',
      'any.required': '新密码是必需的'
    })
});

/**
 * 密码重置验证模式
 */
const resetPasswordSchema = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'any.required': '重置令牌是必需的'
    }),
  
  newPassword: Joi.string()
    .min(8)
    .max(128)
    .required()
    .messages({
      'string.min': '新密码长度至少8个字符',
      'string.max': '新密码长度不能超过128个字符',
      'any.required': '新密码是必需的'
    })
});

/**
 * 忘记密码验证模式
 */
const forgotPasswordSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '邮箱格式不正确',
      'any.required': '邮箱是必需的'
    })
});

/**
 * 用户资料更新验证模式
 */
const updateProfileSchema = Joi.object({
  nickname: Joi.string()
    .min(1)
    .max(50)
    .optional()
    .messages({
      'string.min': '昵称不能为空',
      'string.max': '昵称长度不能超过50个字符'
    }),
  
  firstName: Joi.string()
    .min(1)
    .max(50)
    .optional()
    .messages({
      'string.min': '名字不能为空',
      'string.max': '名字长度不能超过50个字符'
    }),
  
  lastName: Joi.string()
    .min(1)
    .max(50)
    .optional()
    .messages({
      'string.min': '姓氏不能为空',
      'string.max': '姓氏长度不能超过50个字符'
    }),
  
  avatar: Joi.string()
    .uri()
    .optional()
    .messages({
      'string.uri': '头像URL格式不正确'
    })
});

/**
 * MFA启用验证模式
 */
const enableMfaSchema = Joi.object({
  method: Joi.string()
    .valid('totp', 'email', 'sms')
    .required()
    .messages({
      'any.only': 'MFA方法必须是totp、email或sms之一',
      'any.required': 'MFA方法是必需的'
    }),
  
  phoneNumber: Joi.string()
    .pattern(/^\+?[1-9]\d{1,14}$/)
    .when('method', {
      is: 'sms',
      then: Joi.required(),
      otherwise: Joi.optional()
    })
    .messages({
      'string.pattern.base': '手机号格式不正确',
      'any.required': '启用短信MFA时手机号是必需的'
    }),
  
  emailAddress: Joi.string()
    .email()
    .when('method', {
      is: 'email',
      then: Joi.required(),
      otherwise: Joi.optional()
    })
    .messages({
      'string.email': '邮箱格式不正确',
      'any.required': '启用邮件MFA时邮箱是必需的'
    })
});

/**
 * MFA验证验证模式
 */
const verifyMfaSchema = Joi.object({
  method: Joi.string()
    .valid('totp', 'email', 'sms')
    .required()
    .messages({
      'any.only': 'MFA方法必须是totp、email或sms之一',
      'any.required': 'MFA方法是必需的'
    }),
  
  code: Joi.string()
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      'string.pattern.base': '验证码必须是6位数字',
      'any.required': '验证码是必需的'
    })
});

/**
 * 通用验证函数
 * @param schema Joi验证模式
 * @param data 待验证数据
 * @returns 验证结果
 */
function validateData(schema: Joi.ObjectSchema, data: any): ValidationResult {
  const { error, value } = schema.validate(data, { 
    abortEarly: false,
    stripUnknown: true
  });

  if (error) {
    const errors = error.details.map(detail => detail.message);
    return {
      isValid: false,
      errors
    };
  }

  return {
    isValid: true,
    errors: [],
    data: value
  };
}

/**
 * 验证用户注册请求
 */
export function validateRegisterRequest(data: any): ValidationResult {
  return validateData(registerSchema, data);
}

/**
 * 验证用户登录请求
 */
export function validateLoginRequest(data: any): ValidationResult {
  return validateData(loginSchema, data);
}

/**
 * 验证密码修改请求
 */
export function validateChangePasswordRequest(data: any): ValidationResult {
  return validateData(changePasswordSchema, data);
}

/**
 * 验证密码重置请求
 */
export function validateResetPasswordRequest(data: any): ValidationResult {
  return validateData(resetPasswordSchema, data);
}

/**
 * 验证忘记密码请求
 */
export function validateForgotPasswordRequest(data: any): ValidationResult {
  return validateData(forgotPasswordSchema, data);
}

/**
 * 验证用户资料更新请求
 */
export function validateUpdateProfileRequest(data: any): ValidationResult {
  return validateData(updateProfileSchema, data);
}

/**
 * 验证MFA启用请求
 */
export function validateEnableMfaRequest(data: any): ValidationResult {
  return validateData(enableMfaSchema, data);
}

/**
 * 验证MFA验证请求
 */
export function validateVerifyMfaRequest(data: any): ValidationResult {
  return validateData(verifyMfaSchema, data);
}

/**
 * 验证邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证手机号格式
 */
export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
}

/**
 * 验证UUID格式
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

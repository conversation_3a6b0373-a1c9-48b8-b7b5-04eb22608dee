/**
 * JWT工具类
 * 处理JWT令牌的生成、验证和管理
 */

import jwt from 'jsonwebtoken';
import { config } from '@/config';
import { logger } from '@/config/logger';
import { User } from '@prisma/client';

/**
 * JWT载荷接口
 */
export interface JWTPayload {
  userId: string;
  email: string;
  type: 'access' | 'refresh';
  sessionId?: string;
  iat?: number;
  exp?: number;
}

/**
 * 令牌对接口
 */
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

/**
 * 生成访问令牌
 * @param user 用户信息
 * @param sessionId 会话ID（可选）
 * @returns 访问令牌
 */
export function generateAccessToken(user: User, sessionId?: string): string {
  const payload: JWTPayload = {
    userId: user.id,
    email: user.email,
    type: 'access',
    sessionId: sessionId || ''
  };

  try {
    const token = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: 'id-provider',
      audience: 'id-provider-clients'
    } as any);

    logger.debug('访问令牌生成成功', { userId: user.id, sessionId });
    return token;
  } catch (error) {
    logger.error('访问令牌生成失败', { error, userId: user.id });
    throw new Error('令牌生成失败');
  }
}

/**
 * 生成刷新令牌
 * @param user 用户信息
 * @param sessionId 会话ID（可选）
 * @returns 刷新令牌
 */
export function generateRefreshToken(user: User, sessionId?: string): string {
  const payload: JWTPayload = {
    userId: user.id,
    email: user.email,
    type: 'refresh',
    sessionId: sessionId || ''
  };

  try {
    const token = jwt.sign(payload, config.jwt.refreshSecret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: 'id-provider',
      audience: 'id-provider-clients'
    } as any);

    logger.debug('刷新令牌生成成功', { userId: user.id, sessionId });
    return token;
  } catch (error) {
    logger.error('刷新令牌生成失败', { error, userId: user.id });
    throw new Error('令牌生成失败');
  }
}

/**
 * 生成令牌对
 * @param user 用户信息
 * @param sessionId 会话ID（可选）
 * @returns 令牌对
 */
export function generateTokenPair(user: User, sessionId?: string): TokenPair {
  const accessToken = generateAccessToken(user, sessionId);
  const refreshToken = generateRefreshToken(user, sessionId);
  
  // 计算访问令牌过期时间（秒）
  const expiresIn = getTokenExpirationTime(config.jwt.expiresIn);

  return {
    accessToken,
    refreshToken,
    expiresIn
  };
}

/**
 * 验证访问令牌
 * @param token 令牌字符串
 * @returns 解码后的载荷
 */
export function verifyAccessToken(token: string): JWTPayload {
  try {
    const payload = jwt.verify(token, config.jwt.secret, {
      issuer: 'id-provider',
      audience: 'id-provider-clients'
    }) as JWTPayload;

    if (payload.type !== 'access') {
      throw new Error('无效的令牌类型');
    }

    logger.debug('访问令牌验证成功', { userId: payload.userId });
    return payload;
  } catch (error) {
    logger.warn('访问令牌验证失败', { error: (error as Error).message });
    throw new Error('无效的访问令牌');
  }
}

/**
 * 验证刷新令牌
 * @param token 令牌字符串
 * @returns 解码后的载荷
 */
export function verifyRefreshToken(token: string): JWTPayload {
  try {
    const payload = jwt.verify(token, config.jwt.refreshSecret, {
      issuer: 'id-provider',
      audience: 'id-provider-clients'
    }) as JWTPayload;

    if (payload.type !== 'refresh') {
      throw new Error('无效的令牌类型');
    }

    logger.debug('刷新令牌验证成功', { userId: payload.userId });
    return payload;
  } catch (error) {
    logger.warn('刷新令牌验证失败', { error: (error as Error).message });
    throw new Error('无效的刷新令牌');
  }
}

/**
 * 解码令牌（不验证签名）
 * @param token 令牌字符串
 * @returns 解码后的载荷
 */
export function decodeToken(token: string): JWTPayload | null {
  try {
    const payload = jwt.decode(token) as JWTPayload;
    return payload;
  } catch (error) {
    logger.warn('令牌解码失败', { error: (error as Error).message });
    return null;
  }
}

/**
 * 检查令牌是否即将过期
 * @param token 令牌字符串
 * @param thresholdMinutes 阈值（分钟）
 * @returns 是否即将过期
 */
export function isTokenExpiringSoon(token: string, thresholdMinutes: number = 5): boolean {
  try {
    const payload = decodeToken(token);
    if (!payload || !payload.exp) {
      return true;
    }

    const now = Math.floor(Date.now() / 1000);
    const threshold = thresholdMinutes * 60;
    
    return (payload.exp - now) <= threshold;
  } catch (error) {
    return true;
  }
}

/**
 * 获取令牌剩余有效时间（秒）
 * @param token 令牌字符串
 * @returns 剩余时间（秒），-1表示已过期或无效
 */
export function getTokenRemainingTime(token: string): number {
  try {
    const payload = decodeToken(token);
    if (!payload || !payload.exp) {
      return -1;
    }

    const now = Math.floor(Date.now() / 1000);
    const remaining = payload.exp - now;
    
    return remaining > 0 ? remaining : -1;
  } catch (error) {
    return -1;
  }
}

/**
 * 将时间字符串转换为秒数
 * @param timeString 时间字符串（如 '15m', '7d', '1h'）
 * @returns 秒数
 */
function getTokenExpirationTime(timeString: string): number {
  const match = timeString.match(/^(\d+)([smhd])$/);
  if (!match) {
    throw new Error('无效的时间格式');
  }

  const value = parseInt(match[1] || '0', 10);
  const unit = match[2] || 's';

  switch (unit) {
    case 's':
      return value;
    case 'm':
      return value * 60;
    case 'h':
      return value * 60 * 60;
    case 'd':
      return value * 60 * 60 * 24;
    default:
      throw new Error('不支持的时间单位');
  }
}

/**
 * 生成用于OIDC的ID令牌
 * @param user 用户信息
 * @param clientId 客户端ID
 * @param nonce 随机数（可选）
 * @returns ID令牌
 */
export function generateIdToken(user: User, clientId: string, nonce?: string): string {
  const payload = {
    sub: user.id,
    email: user.email,
    email_verified: user.emailVerified,
    name: user.nickname || `${user.firstName || ''} ${user.lastName || ''}`.trim(),
    given_name: user.firstName,
    family_name: user.lastName,
    picture: user.avatar,
    aud: clientId,
    iss: 'id-provider',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + getTokenExpirationTime(config.jwt.expiresIn),
    ...(nonce && { nonce })
  };

  try {
    const token = jwt.sign(payload, config.jwt.secret, {
      algorithm: 'RS256' // OIDC要求使用RS256
    });

    logger.debug('ID令牌生成成功', { userId: user.id, clientId });
    return token;
  } catch (error) {
    logger.error('ID令牌生成失败', { error, userId: user.id, clientId });
    throw new Error('ID令牌生成失败');
  }
}

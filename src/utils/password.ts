/**
 * 密码工具类
 * 处理密码加密、验证和策略检查
 */

import bcrypt from 'bcrypt';
import crypto from 'crypto';
import { config } from '@/config';
import { logger } from '@/config/logger';
import { PasswordPolicy } from '@/types/database';

/**
 * 密码强度检查结果
 */
export interface PasswordStrengthResult {
  isValid: boolean;
  score: number; // 0-100
  errors: string[];
  suggestions: string[];
}

/**
 * 默认密码策略
 */
const DEFAULT_PASSWORD_POLICY: PasswordPolicy = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  maxAge: 90,
  historyCount: 5
};

/**
 * 加密密码
 * @param password 明文密码
 * @returns 加密后的密码哈希
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    const saltRounds = config.security.bcryptRounds;
    const hash = await bcrypt.hash(password, saltRounds);
    
    logger.debug('密码加密成功');
    return hash;
  } catch (error) {
    logger.error('密码加密失败', { error });
    throw new Error('密码加密失败');
  }
}

/**
 * 验证密码
 * @param password 明文密码
 * @param hash 密码哈希
 * @returns 是否匹配
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    const isMatch = await bcrypt.compare(password, hash);
    
    logger.debug('密码验证完成', { isMatch });
    return isMatch;
  } catch (error) {
    logger.error('密码验证失败', { error });
    return false;
  }
}

/**
 * 检查密码强度
 * @param password 密码
 * @param policy 密码策略（可选）
 * @returns 密码强度检查结果
 */
export function checkPasswordStrength(
  password: string, 
  policy: PasswordPolicy = DEFAULT_PASSWORD_POLICY
): PasswordStrengthResult {
  const errors: string[] = [];
  const suggestions: string[] = [];
  let score = 0;

  // 检查长度
  if (password.length < policy.minLength) {
    errors.push(`密码长度至少需要${policy.minLength}个字符`);
  } else {
    score += 20;
    if (password.length >= 12) score += 10;
    if (password.length >= 16) score += 10;
  }

  // 检查大写字母
  if (policy.requireUppercase) {
    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含至少一个大写字母');
      suggestions.push('添加大写字母');
    } else {
      score += 15;
    }
  }

  // 检查小写字母
  if (policy.requireLowercase) {
    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含至少一个小写字母');
      suggestions.push('添加小写字母');
    } else {
      score += 15;
    }
  }

  // 检查数字
  if (policy.requireNumbers) {
    if (!/\d/.test(password)) {
      errors.push('密码必须包含至少一个数字');
      suggestions.push('添加数字');
    } else {
      score += 15;
    }
  }

  // 检查特殊字符
  if (policy.requireSpecialChars) {
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('密码必须包含至少一个特殊字符');
      suggestions.push('添加特殊字符 (!@#$%^&*等)');
    } else {
      score += 15;
    }
  }

  // 检查常见模式
  if (/(.)\1{2,}/.test(password)) {
    score -= 10;
    suggestions.push('避免连续重复字符');
  }

  if (/123|abc|qwe|password|admin/i.test(password)) {
    score -= 20;
    suggestions.push('避免使用常见的字符序列或单词');
  }

  // 检查字符多样性
  const uniqueChars = new Set(password).size;
  const diversity = uniqueChars / password.length;
  if (diversity > 0.7) {
    score += 10;
  } else if (diversity < 0.5) {
    suggestions.push('增加字符多样性');
  }

  // 确保分数在0-100范围内
  score = Math.max(0, Math.min(100, score));

  const isValid = errors.length === 0 && score >= 60;

  return {
    isValid,
    score,
    errors,
    suggestions
  };
}

/**
 * 生成安全的随机密码
 * @param length 密码长度
 * @param includeSymbols 是否包含特殊字符
 * @returns 随机密码
 */
export function generateSecurePassword(length: number = 12, includeSymbols: boolean = true): string {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  let charset = lowercase + uppercase + numbers;
  if (includeSymbols) {
    charset += symbols;
  }

  let password = '';
  
  // 确保至少包含每种类型的字符
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  
  if (includeSymbols) {
    password += symbols[Math.floor(Math.random() * symbols.length)];
  }

  // 填充剩余长度
  for (let i = password.length; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }

  // 打乱字符顺序
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * 生成密码重置令牌
 * @returns 重置令牌
 */
export function generatePasswordResetToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * 生成邮箱验证令牌
 * @returns 验证令牌
 */
export function generateEmailVerificationToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * 生成短验证码（用于邮件/短信）
 * @param length 验证码长度
 * @returns 验证码
 */
export function generateVerificationCode(length: number = 6): string {
  const digits = '0123456789';
  let code = '';
  
  for (let i = 0; i < length; i++) {
    code += digits[Math.floor(Math.random() * digits.length)];
  }
  
  return code;
}

/**
 * 检查密码是否在历史记录中
 * @param password 新密码
 * @param passwordHistory 历史密码哈希数组
 * @returns 是否在历史记录中
 */
export async function isPasswordInHistory(
  password: string, 
  passwordHistory: string[]
): Promise<boolean> {
  try {
    for (const historyHash of passwordHistory) {
      if (await verifyPassword(password, historyHash)) {
        return true;
      }
    }
    return false;
  } catch (error) {
    logger.error('检查密码历史失败', { error });
    return false;
  }
}

/**
 * 计算密码熵值
 * @param password 密码
 * @returns 熵值（比特）
 */
export function calculatePasswordEntropy(password: string): number {
  let charset = 0;
  
  if (/[a-z]/.test(password)) charset += 26;
  if (/[A-Z]/.test(password)) charset += 26;
  if (/\d/.test(password)) charset += 10;
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) charset += 32;
  
  return Math.log2(Math.pow(charset, password.length));
}

/**
 * 检查密码是否为常见弱密码
 * @param password 密码
 * @returns 是否为弱密码
 */
export function isCommonWeakPassword(password: string): boolean {
  const commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123',
    'password123', 'admin', 'letmein', 'welcome', 'monkey',
    '1234567890', 'qwertyuiop', 'asdfghjkl', 'zxcvbnm'
  ];
  
  return commonPasswords.includes(password.toLowerCase());
}

/**
 * 生成密码策略描述文本
 * @param policy 密码策略
 * @returns 策略描述
 */
export function getPasswordPolicyDescription(policy: PasswordPolicy): string {
  const requirements: string[] = [];
  
  requirements.push(`至少${policy.minLength}个字符`);
  
  if (policy.requireUppercase) {
    requirements.push('包含大写字母');
  }
  
  if (policy.requireLowercase) {
    requirements.push('包含小写字母');
  }
  
  if (policy.requireNumbers) {
    requirements.push('包含数字');
  }
  
  if (policy.requireSpecialChars) {
    requirements.push('包含特殊字符');
  }
  
  return `密码要求：${requirements.join('、')}`;
}

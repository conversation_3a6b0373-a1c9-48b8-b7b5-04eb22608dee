/**
 * 缓存服务
 * 提供高级缓存功能和业务逻辑缓存
 */

import { redisService, RedisKeys, RedisTTL } from './redis.service';
import { logger } from '@/config/logger';

export interface CacheOptions {
  ttl?: number;
  prefix?: string;
  serialize?: boolean;
}

export interface SessionData {
  userId: string;
  email: string;
  roles: string[];
  permissions: string[];
  deviceInfo?: any;
  ipAddress?: string;
  lastAccessedAt: Date;
  expiresAt: Date;
}

export interface UserProfile {
  id: string;
  email: string;
  nickname?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  isEmailVerified: boolean;
  mfaEnabled: boolean;
  roles: string[];
  createdAt: Date;
  lastLoginAt?: Date;
}

class CacheService {
  
  // ==================== 会话缓存 ====================

  /**
   * 缓存用户会话
   */
  async setSession(sessionId: string, sessionData: SessionData): Promise<void> {
    try {
      const key = RedisKeys.SESSION(sessionId);
      await redisService.set(key, sessionData, RedisTTL.SESSION);
      
      // 同时更新用户会话列表
      await this.addUserSession(sessionData.userId, sessionId);
      
      logger.debug('会话缓存成功', { sessionId, userId: sessionData.userId });
    } catch (error) {
      logger.error('会话缓存失败', {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取用户会话
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const key = RedisKeys.SESSION(sessionId);
      const sessionData = await redisService.get<SessionData>(key);
      
      if (sessionData) {
        logger.debug('会话获取成功', { sessionId });
      }
      
      return sessionData;
    } catch (error) {
      logger.error('会话获取失败', {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 删除用户会话
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      // 先获取会话数据以获取用户ID
      const sessionData = await this.getSession(sessionId);
      
      const key = RedisKeys.SESSION(sessionId);
      await redisService.del(key);
      
      // 从用户会话列表中移除
      if (sessionData) {
        await this.removeUserSession(sessionData.userId, sessionId);
      }
      
      logger.debug('会话删除成功', { sessionId });
    } catch (error) {
      logger.error('会话删除失败', {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 添加用户会话到列表
   */
  async addUserSession(userId: string, sessionId: string): Promise<void> {
    try {
      const key = RedisKeys.USER_SESSIONS(userId);
      const client = redisService.getClient();
      
      // 使用Redis Set存储用户的所有会话ID
      await client.sadd(key, sessionId);
      await redisService.expire(key, RedisTTL.USER_SESSIONS);
      
      logger.debug('用户会话添加成功', { userId, sessionId });
    } catch (error) {
      logger.error('用户会话添加失败', {
        userId,
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 从用户会话列表中移除
   */
  async removeUserSession(userId: string, sessionId: string): Promise<void> {
    try {
      const key = RedisKeys.USER_SESSIONS(userId);
      const client = redisService.getClient();
      
      await client.srem(key, sessionId);
      
      logger.debug('用户会话移除成功', { userId, sessionId });
    } catch (error) {
      logger.error('用户会话移除失败', {
        userId,
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取用户所有会话
   */
  async getUserSessions(userId: string): Promise<string[]> {
    try {
      const key = RedisKeys.USER_SESSIONS(userId);
      const client = redisService.getClient();
      
      const sessionIds = await client.smembers(key);
      logger.debug('用户会话列表获取成功', { userId, sessionCount: sessionIds.length });
      
      return sessionIds;
    } catch (error) {
      logger.error('用户会话列表获取失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * 删除用户所有会话
   */
  async deleteAllUserSessions(userId: string): Promise<void> {
    try {
      const sessionIds = await this.getUserSessions(userId);
      
      // 删除所有会话数据
      const sessionKeys = sessionIds.map(id => RedisKeys.SESSION(id));
      if (sessionKeys.length > 0) {
        await redisService.del(sessionKeys);
      }
      
      // 删除用户会话列表
      const userSessionsKey = RedisKeys.USER_SESSIONS(userId);
      await redisService.del(userSessionsKey);
      
      logger.info('用户所有会话删除成功', { userId, sessionCount: sessionIds.length });
    } catch (error) {
      logger.error('用户所有会话删除失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // ==================== JWT令牌管理 ====================

  /**
   * 将JWT令牌加入黑名单
   */
  async blacklistJWT(jti: string, exp: number): Promise<void> {
    try {
      const key = RedisKeys.JWT_BLACKLIST(jti);
      const ttl = Math.max(0, exp - Math.floor(Date.now() / 1000));
      
      await redisService.set(key, true, ttl);
      
      logger.debug('JWT令牌加入黑名单', { jti, ttl });
    } catch (error) {
      logger.error('JWT令牌黑名单添加失败', {
        jti,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 检查JWT令牌是否在黑名单中
   */
  async isJWTBlacklisted(jti: string): Promise<boolean> {
    try {
      const key = RedisKeys.JWT_BLACKLIST(jti);
      return await redisService.exists(key);
    } catch (error) {
      logger.error('JWT令牌黑名单检查失败', {
        jti,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 缓存刷新令牌
   */
  async setRefreshToken(tokenId: string, tokenData: any): Promise<void> {
    try {
      const key = RedisKeys.REFRESH_TOKEN(tokenId);
      await redisService.set(key, tokenData, RedisTTL.REFRESH_TOKEN);
      
      logger.debug('刷新令牌缓存成功', { tokenId });
    } catch (error) {
      logger.error('刷新令牌缓存失败', {
        tokenId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取刷新令牌
   */
  async getRefreshToken(tokenId: string): Promise<any> {
    try {
      const key = RedisKeys.REFRESH_TOKEN(tokenId);
      return await redisService.get(key);
    } catch (error) {
      logger.error('刷新令牌获取失败', {
        tokenId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 删除刷新令牌
   */
  async deleteRefreshToken(tokenId: string): Promise<void> {
    try {
      const key = RedisKeys.REFRESH_TOKEN(tokenId);
      await redisService.del(key);
      
      logger.debug('刷新令牌删除成功', { tokenId });
    } catch (error) {
      logger.error('刷新令牌删除失败', {
        tokenId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // ==================== 速率限制 ====================

  /**
   * 检查和更新速率限制
   */
  async checkRateLimit(key: string, limit: number, windowMs: number): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    try {
      const client = redisService.getClient();
      const now = Date.now();
      const window = Math.floor(now / windowMs);
      const rateLimitKey = `${key}:${window}`;
      
      const current = await client.incr(rateLimitKey);
      
      if (current === 1) {
        // 第一次请求，设置过期时间
        await client.expire(rateLimitKey, Math.ceil(windowMs / 1000));
      }
      
      const allowed = current <= limit;
      const remaining = Math.max(0, limit - current);
      const resetTime = (window + 1) * windowMs;
      
      logger.debug('速率限制检查', {
        key,
        current,
        limit,
        allowed,
        remaining
      });
      
      return { allowed, remaining, resetTime };
    } catch (error) {
      logger.error('速率限制检查失败', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      // 出错时允许请求
      return { allowed: true, remaining: limit, resetTime: Date.now() + windowMs };
    }
  }

  // ==================== 用户数据缓存 ====================

  /**
   * 缓存用户资料
   */
  async setUserProfile(userId: string, profile: UserProfile): Promise<void> {
    try {
      const key = RedisKeys.USER_PROFILE(userId);
      await redisService.set(key, profile, RedisTTL.USER_PROFILE);
      
      logger.debug('用户资料缓存成功', { userId });
    } catch (error) {
      logger.error('用户资料缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取用户资料
   */
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const key = RedisKeys.USER_PROFILE(userId);
      return await redisService.get<UserProfile>(key);
    } catch (error) {
      logger.error('用户资料获取失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 删除用户资料缓存
   */
  async deleteUserProfile(userId: string): Promise<void> {
    try {
      const key = RedisKeys.USER_PROFILE(userId);
      await redisService.del(key);
      
      logger.debug('用户资料缓存删除成功', { userId });
    } catch (error) {
      logger.error('用户资料缓存删除失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 缓存用户权限
   */
  async setUserPermissions(userId: string, permissions: string[]): Promise<void> {
    try {
      const key = RedisKeys.USER_PERMISSIONS(userId);
      await redisService.set(key, permissions, RedisTTL.USER_PERMISSIONS);
      
      logger.debug('用户权限缓存成功', { userId, permissionCount: permissions.length });
    } catch (error) {
      logger.error('用户权限缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取用户权限
   */
  async getUserPermissions(userId: string): Promise<string[] | null> {
    try {
      const key = RedisKeys.USER_PERMISSIONS(userId);
      return await redisService.get<string[]>(key);
    } catch (error) {
      logger.error('用户权限获取失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  // ==================== OAuth状态管理 ====================

  /**
   * 缓存OAuth状态
   */
  async setOAuthState(state: string, data: any): Promise<void> {
    try {
      const key = RedisKeys.OAUTH_STATE(state);
      await redisService.set(key, data, RedisTTL.OAUTH_STATE);
      
      logger.debug('OAuth状态缓存成功', { state });
    } catch (error) {
      logger.error('OAuth状态缓存失败', {
        state,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取OAuth状态
   */
  async getOAuthState(state: string): Promise<any> {
    try {
      const key = RedisKeys.OAUTH_STATE(state);
      return await redisService.get(key);
    } catch (error) {
      logger.error('OAuth状态获取失败', {
        state,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 删除OAuth状态
   */
  async deleteOAuthState(state: string): Promise<void> {
    try {
      const key = RedisKeys.OAUTH_STATE(state);
      await redisService.del(key);
      
      logger.debug('OAuth状态删除成功', { state });
    } catch (error) {
      logger.error('OAuth状态删除失败', {
        state,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // ==================== MFA验证码管理 ====================

  /**
   * 缓存MFA验证码
   */
  async setMFACode(userId: string, type: string, code: string): Promise<void> {
    try {
      const key = RedisKeys.MFA_CODE(userId, type);
      await redisService.set(key, code, RedisTTL.MFA_CODE);
      
      logger.debug('MFA验证码缓存成功', { userId, type });
    } catch (error) {
      logger.error('MFA验证码缓存失败', {
        userId,
        type,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取MFA验证码
   */
  async getMFACode(userId: string, type: string): Promise<string | null> {
    try {
      const key = RedisKeys.MFA_CODE(userId, type);
      return await redisService.get<string>(key);
    } catch (error) {
      logger.error('MFA验证码获取失败', {
        userId,
        type,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 删除MFA验证码
   */
  async deleteMFACode(userId: string, type: string): Promise<void> {
    try {
      const key = RedisKeys.MFA_CODE(userId, type);
      await redisService.del(key);
      
      logger.debug('MFA验证码删除成功', { userId, type });
    } catch (error) {
      logger.error('MFA验证码删除失败', {
        userId,
        type,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // ==================== 通用缓存方法 ====================

  /**
   * 通用缓存设置
   */
  async cache<T>(key: string, value: T, ttl?: number): Promise<void> {
    await redisService.set(key, value, ttl);
  }

  /**
   * 通用缓存获取
   */
  async getCached<T>(key: string): Promise<T | null> {
    return await redisService.get<T>(key);
  }

  /**
   * 通用缓存删除
   */
  async invalidate(key: string | string[]): Promise<void> {
    await redisService.del(key);
  }

  /**
   * 清理过期缓存
   */
  async cleanup(): Promise<void> {
    try {
      // Redis会自动清理过期键，这里可以添加自定义清理逻辑
      logger.info('缓存清理完成');
    } catch (error) {
      logger.error('缓存清理失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}

// 创建单例实例
export const cacheService = new CacheService();

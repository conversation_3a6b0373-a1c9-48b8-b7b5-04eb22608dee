/**
 * 协议适配器服务
 * 管理和协调各种协议适配器
 */

import { 
  IProtocolAdapter, 
  IProtocolAdapterFactory, 
  ProtocolConfig, 
  AuthenticationRequest, 
  AuthenticationResponse,
  ProtocolAdapterError,
  ApplicationProtocolConfig
} from '@/types/protocol-adapter';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { EventEmitter } from 'events';

/**
 * 协议适配器工厂实现
 */
export class ProtocolAdapterFactory implements IProtocolAdapterFactory {
  private adapters: Map<string, new () => IProtocolAdapter> = new Map();

  constructor() {
    this.registerBuiltInAdapters();
  }

  /**
   * 注册内置适配器
   */
  private registerBuiltInAdapters(): void {
    // 这里会注册内置的协议适配器
    // this.registerAdapter('oidc', OIDCAdapter);
    // this.registerAdapter('saml', SAMLAdapter);
    // this.registerAdapter('custom-oauth', CustomOAuthAdapter);
  }

  /**
   * 创建协议适配器实例
   */
  async createAdapter(protocolName: string, config: ProtocolConfig): Promise<IProtocolAdapter> {
    const AdapterClass = this.adapters.get(protocolName);
    if (!AdapterClass) {
      throw new ProtocolAdapterError(
        'ADAPTER_NOT_FOUND',
        `协议适配器 ${protocolName} 未找到`
      );
    }

    const adapter = new AdapterClass();
    await adapter.initialize(config);
    
    logger.info('协议适配器创建成功', { 
      protocolName, 
      version: adapter.version 
    });

    return adapter;
  }

  /**
   * 获取支持的协议列表
   */
  getSupportedProtocols(): string[] {
    return Array.from(this.adapters.keys());
  }

  /**
   * 注册新的协议适配器
   */
  registerAdapter(protocolName: string, adapterClass: new () => IProtocolAdapter): void {
    this.adapters.set(protocolName, adapterClass);
    logger.info('协议适配器注册成功', { protocolName });
  }

  /**
   * 注销协议适配器
   */
  unregisterAdapter(protocolName: string): void {
    this.adapters.delete(protocolName);
    logger.info('协议适配器注销成功', { protocolName });
  }
}

/**
 * 协议适配器服务
 */
export class ProtocolAdapterService extends EventEmitter {
  private factory: ProtocolAdapterFactory;
  private activeAdapters: Map<string, IProtocolAdapter> = new Map();
  private applicationConfigs: Map<string, ApplicationProtocolConfig> = new Map();

  constructor() {
    super();
    this.factory = new ProtocolAdapterFactory();
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    try {
      // 加载应用协议配置
      await this.loadApplicationConfigs();
      
      // 初始化活跃的适配器
      await this.initializeActiveAdapters();

      logger.info('协议适配器服务初始化完成');
    } catch (error) {
      logger.error('协议适配器服务初始化失败', { error });
      throw error;
    }
  }

  /**
   * 加载应用协议配置
   */
  private async loadApplicationConfigs(): Promise<void> {
    try {
      // 从数据库加载应用配置
      const applications = await prisma.application.findMany({
        where: { isActive: true },
        select: {
          id: true,
          clientId: true,
          supportedProtocols: true,
          oidcConfig: true,
          samlConfig: true
        }
      });

      for (const app of applications) {
        for (const protocol of (app.supportedProtocols as string[])) {
          const config = this.buildProtocolConfig(app, protocol);
          if (config) {
            this.applicationConfigs.set(`${app.id}:${protocol}`, config);
          }
        }
      }

      logger.info('应用协议配置加载完成', { 
        count: this.applicationConfigs.size 
      });
    } catch (error) {
      logger.error('加载应用协议配置失败', { error });
      throw error;
    }
  }

  /**
   * 构建协议配置
   */
  private buildProtocolConfig(
    app: any, 
    protocolName: string
  ): ApplicationProtocolConfig | null {
    try {
      let protocolConfig: ProtocolConfig;

      switch (protocolName) {
        case 'oidc':
          protocolConfig = app.oidcConfig as ProtocolConfig;
          break;
        case 'saml':
          protocolConfig = app.samlConfig as ProtocolConfig;
          break;
        default:
          // 对于自定义协议，从扩展配置中获取
          return null;
      }

      if (!protocolConfig) {
        return null;
      }

      return {
        applicationId: app.id,
        protocolName,
        protocolVersion: protocolConfig.version || '1.0',
        config: protocolConfig,
        isActive: true
      };
    } catch (error) {
      logger.error('构建协议配置失败', { 
        applicationId: app.id, 
        protocolName, 
        error 
      });
      return null;
    }
  }

  /**
   * 初始化活跃的适配器
   */
  private async initializeActiveAdapters(): Promise<void> {
    const protocolCounts = new Map<string, number>();

    // 统计每种协议的使用次数
    for (const config of this.applicationConfigs.values()) {
      const count = protocolCounts.get(config.protocolName) || 0;
      protocolCounts.set(config.protocolName, count + 1);
    }

    // 为每种使用的协议创建适配器实例
    for (const [protocolName, count] of protocolCounts) {
      try {
        // 使用第一个配置作为默认配置
        const firstConfig = Array.from(this.applicationConfigs.values())
          .find(c => c.protocolName === protocolName);

        if (firstConfig) {
          const adapter = await this.factory.createAdapter(
            protocolName, 
            firstConfig.config
          );
          this.activeAdapters.set(protocolName, adapter);
        }
      } catch (error) {
        logger.error('初始化协议适配器失败', { 
          protocolName, 
          error 
        });
      }
    }

    logger.info('活跃协议适配器初始化完成', { 
      protocols: Array.from(this.activeAdapters.keys()) 
    });
  }

  /**
   * 处理认证请求
   */
  async handleAuthRequest(
    applicationId: string, 
    protocolName: string, 
    request: AuthenticationRequest
  ): Promise<AuthenticationResponse> {
    try {
      const configKey = `${applicationId}:${protocolName}`;
      const appConfig = this.applicationConfigs.get(configKey);

      if (!appConfig) {
        throw new ProtocolAdapterError(
          'CONFIG_NOT_FOUND',
          `应用 ${applicationId} 的 ${protocolName} 协议配置未找到`
        );
      }

      const adapter = this.activeAdapters.get(protocolName);
      if (!adapter) {
        throw new ProtocolAdapterError(
          'ADAPTER_NOT_ACTIVE',
          `协议适配器 ${protocolName} 未激活`
        );
      }

      // 执行预认证处理器
      if (appConfig.customHandlers?.preAuth) {
        await this.executeCustomHandler(
          appConfig.customHandlers.preAuth, 
          request
        );
      }

      // 处理认证请求
      const response = await adapter.handleAuthRequest(request);

      // 执行后认证处理器
      if (appConfig.customHandlers?.postAuth && response.success) {
        await this.executeCustomHandler(
          appConfig.customHandlers.postAuth, 
          response
        );
      }

      // 发送成功事件
      if (response.success && appConfig.webhooks?.onSuccess) {
        this.emit('auth_success', {
          applicationId,
          protocolName,
          response,
          webhookUrl: appConfig.webhooks.onSuccess
        });
      }

      // 发送错误事件
      if (!response.success && appConfig.webhooks?.onError) {
        this.emit('auth_error', {
          applicationId,
          protocolName,
          response,
          webhookUrl: appConfig.webhooks.onError
        });
      }

      return response;

    } catch (error) {
      logger.error('处理认证请求失败', { 
        applicationId, 
        protocolName, 
        error 
      });

      return {
        success: false,
        error: {
          code: error instanceof ProtocolAdapterError ? error.code : 'INTERNAL_ERROR',
          message: error.message
        }
      };
    }
  }

  /**
   * 执行自定义处理器
   */
  private async executeCustomHandler(
    handlerName: string, 
    data: any
  ): Promise<void> {
    try {
      // 这里可以实现动态加载和执行自定义处理器的逻辑
      logger.info('执行自定义处理器', { handlerName });
      
      // 示例：从插件系统加载处理器
      // const handler = await this.pluginManager.getHandler(handlerName);
      // if (handler) {
      //   await handler(data);
      // }
    } catch (error) {
      logger.error('执行自定义处理器失败', { 
        handlerName, 
        error 
      });
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 获取支持的协议列表
   */
  getSupportedProtocols(): string[] {
    return this.factory.getSupportedProtocols();
  }

  /**
   * 获取应用的协议配置
   */
  getApplicationConfig(
    applicationId: string, 
    protocolName: string
  ): ApplicationProtocolConfig | undefined {
    return this.applicationConfigs.get(`${applicationId}:${protocolName}`);
  }

  /**
   * 注册协议适配器
   */
  registerProtocolAdapter(
    protocolName: string, 
    adapterClass: new () => IProtocolAdapter
  ): void {
    this.factory.registerAdapter(protocolName, adapterClass);
  }

  /**
   * 重新加载配置
   */
  async reloadConfigs(): Promise<void> {
    this.applicationConfigs.clear();
    this.activeAdapters.clear();
    await this.initialize();
  }
}

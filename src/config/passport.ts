/**
 * Passport.js 配置
 * 配置各种OAuth策略和认证策略
 */

import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as GitHubStrategy } from 'passport-github2';
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt';
import { WeChatStrategy } from '@/strategies/wechat.strategy';
import { WeiboStrategy } from '@/strategies/weibo.strategy';
import { config } from './index';
import { logger } from './logger';
import { prisma } from './database';

/**
 * OAuth用户信息接口
 */
export interface OAuthProfile {
  id: string;
  provider: string;
  email?: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  accessToken?: string;
  refreshToken?: string;
}

/**
 * 配置JWT策略
 */
passport.use(new JwtStrategy({
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  secretOrKey: config.jwt.secret,
  issuer: 'id-provider',
  audience: 'id-provider-users'
}, async (payload, done) => {
  try {
    const user = await prisma.user.findUnique({
      where: { 
        id: payload.userId,
        isActive: true,
        isLocked: false
      },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    });

    if (!user) {
      return done(null, false);
    }

    return done(null, {
      userId: user.id,
      email: user.email,
      roles: user.userRoles.map(ur => ur.role.name)
    });
  } catch (error) {
    logger.error('JWT策略验证失败', { error, payload });
    return done(error, false);
  }
}));

/**
 * 配置Google OAuth策略
 */
if (config.oauth.google.clientId && config.oauth.google.clientSecret) {
  passport.use(new GoogleStrategy({
    clientID: config.oauth.google.clientId,
    clientSecret: config.oauth.google.clientSecret,
    callbackURL: config.oauth.google.callbackUrl,
    scope: ['profile', 'email']
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      const oauthProfile: OAuthProfile = {
        id: profile.id,
        provider: 'google',
        email: profile.emails?.[0]?.value,
        name: profile.displayName,
        firstName: profile.name?.givenName,
        lastName: profile.name?.familyName,
        avatar: profile.photos?.[0]?.value,
        accessToken,
        refreshToken
      };

      logger.info('Google OAuth认证成功', { 
        providerId: oauthProfile.id, 
        email: oauthProfile.email 
      });

      return done(null, oauthProfile);
    } catch (error) {
      logger.error('Google OAuth策略处理失败', { error, profileId: profile.id });
      return done(error, null);
    }
  }));
}

/**
 * 配置GitHub OAuth策略
 */
if (config.oauth.github.clientId && config.oauth.github.clientSecret) {
  passport.use(new GitHubStrategy({
    clientID: config.oauth.github.clientId,
    clientSecret: config.oauth.github.clientSecret,
    callbackURL: config.oauth.github.callbackUrl,
    scope: ['user:email']
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      const oauthProfile: OAuthProfile = {
        id: profile.id,
        provider: 'github',
        email: profile.emails?.[0]?.value,
        name: profile.displayName || profile.username,
        avatar: profile.photos?.[0]?.value,
        accessToken,
        refreshToken
      };

      logger.info('GitHub OAuth认证成功', { 
        providerId: oauthProfile.id, 
        email: oauthProfile.email 
      });

      return done(null, oauthProfile);
    } catch (error) {
      logger.error('GitHub OAuth策略处理失败', { error, profileId: profile.id });
      return done(error, null);
    }
  }));
}

/**
 * 配置微信OAuth策略
 */
if (config.oauth.wechat.appId && config.oauth.wechat.appSecret) {
  passport.use(new WeChatStrategy(async (accessToken, refreshToken, profile, done) => {
    try {
      const oauthProfile: OAuthProfile = {
        id: profile.id,
        provider: 'wechat',
        name: profile.name,
        avatar: profile.avatar,
        accessToken,
        refreshToken
      };

      logger.info('微信OAuth认证成功', {
        providerId: oauthProfile.id
      });

      return done(null, oauthProfile);
    } catch (error) {
      logger.error('微信OAuth策略处理失败', { error, profileId: profile.id });
      return done(error, null);
    }
  }));
}

/**
 * 配置微博OAuth策略
 */
if (config.oauth.weibo.clientId && config.oauth.weibo.clientSecret) {
  passport.use(new WeiboStrategy(async (accessToken, refreshToken, profile, done) => {
    try {
      const oauthProfile: OAuthProfile = {
        id: profile.id,
        provider: 'weibo',
        email: profile.email,
        name: profile.name,
        avatar: profile.avatar,
        accessToken,
        refreshToken
      };

      logger.info('微博OAuth认证成功', {
        providerId: oauthProfile.id,
        email: oauthProfile.email
      });

      return done(null, oauthProfile);
    } catch (error) {
      logger.error('微博OAuth策略处理失败', { error, profileId: profile.id });
      return done(error, null);
    }
  }));
}

/**
 * 序列化用户（存储到session）
 */
passport.serializeUser((user: any, done) => {
  done(null, user);
});

/**
 * 反序列化用户（从session恢复）
 */
passport.deserializeUser((user: any, done) => {
  done(null, user);
});

export default passport;
